/**
 * Test for OvenMediaEngine Webhook Integration
 * Tests the new webhook endpoint that replaces nginx-rtmp functionality
 *
 * Note: OME calls localhost:8081 for internal service communication
 */

const request = require('supertest');
const app = require('../src/app'); // Adjust path as needed

describe('OME Webhook Integration', () => {
  
  describe('POST /v1/api/stream/live', () => {
    
    it('should handle stream start event (opening status)', async () => {
      const omePayload = {
        client: {
          address: "*************",
          port: 12345,
          real_ip: "*************"
        },
        request: {
          direction: "incoming",
          protocol: "rtmp",
          status: "opening",
          url: "rtmp://localhost:1935/app/testuser_teststream123",
          time: new Date().toISOString()
        }
      };

      const response = await request(app)
        .post('/v1/api/stream/live')
        .send(omePayload)
        .expect(200);

      expect(response.body).toHaveProperty('allowed', true);
      expect(response.body).toHaveProperty('reason', 'Stream authorized');
    });

    it('should handle stream end event (closing status)', async () => {
      const omePayload = {
        client: {
          address: "*************",
          port: 12345,
          real_ip: "*************"
        },
        request: {
          direction: "incoming",
          protocol: "rtmp",
          status: "closing",
          url: "rtmp://localhost:1935/app/testuser_teststream123",
          time: new Date().toISOString()
        }
      };

      const response = await request(app)
        .post('/v1/api/stream/live')
        .send(omePayload)
        .expect(200);

      expect(response.body).toEqual({});
    });

    it('should handle other events (playback requests)', async () => {
      const omePayload = {
        client: {
          address: "*************",
          port: 12345,
          real_ip: "*************"
        },
        request: {
          direction: "outgoing",
          protocol: "llhls",
          status: "opening",
          url: "http://localhost:8080/app/teststream/llhls.m3u8",
          time: new Date().toISOString()
        }
      };

      const response = await request(app)
        .post('/v1/api/stream/live')
        .send(omePayload)
        .expect(200);

      expect(response.body).toHaveProperty('allowed', true);
      expect(response.body).toHaveProperty('reason', 'Event allowed');
    });

    it('should handle invalid payload gracefully', async () => {
      const invalidPayload = {
        invalid: "payload"
      };

      const response = await request(app)
        .post('/v1/api/stream/live')
        .send(invalidPayload)
        .expect(400);

      expect(response.body).toHaveProperty('allowed', false);
      expect(response.body).toHaveProperty('reason', 'Invalid webhook payload');
    });

    it('should extract stream key correctly from OME URL format', async () => {
      const omePayload = {
        client: {
          address: "*************",
          port: 12345
        },
        request: {
          direction: "incoming",
          protocol: "rtmp",
          status: "opening",
          url: "rtmp://localhost:1935/app/user123_stream456",
          time: new Date().toISOString()
        }
      };

      // This test verifies that the stream key "user123_stream456" 
      // is correctly extracted from the URL
      const response = await request(app)
        .post('/v1/api/stream/live')
        .send(omePayload)
        .expect(200);

      expect(response.body.allowed).toBe(true);
    });

  });

  describe('Backward Compatibility', () => {
    
    it('should maintain old nginx-rtmp start endpoint', async () => {
      const nginxPayload = {
        name: "testuser_teststream123"
      };

      // This endpoint should still work for backward compatibility
      const response = await request(app)
        .post('/v1/api/stream/live/start')
        .send(nginxPayload);

      // Note: This will fail authentication without proper middleware setup
      // but the route should exist
      expect([200, 401, 403]).toContain(response.status);
    });

    it('should maintain old nginx-rtmp end endpoint', async () => {
      const nginxPayload = {
        name: "testuser_teststream123"
      };

      // This endpoint should still work for backward compatibility
      const response = await request(app)
        .post('/v1/api/stream/live/end')
        .send(nginxPayload);

      // Note: This will fail authentication without proper middleware setup
      // but the route should exist
      expect([200, 401, 403]).toContain(response.status);
    });

  });

});

/**
 * Integration Test Notes:
 * 
 * 1. Single Endpoint: OME uses one endpoint (/live) for both start/end events
 *    Events are distinguished by request.status field
 * 
 * 2. Stream Key Format: OME extracts stream key from URL path
 *    nginx-rtmp: { "name": "user_stream" }
 *    OME: { "request": { "url": "rtmp://host/app/user_stream" } }
 * 
 * 3. Event Detection:
 *    nginx-rtmp: Separate endpoints for start/end
 *    OME: Single endpoint with status field ("opening"/"closing")
 * 
 * 4. Response Format:
 *    nginx-rtmp: Plain text or simple JSON
 *    OME: Structured JSON with "allowed" field
 * 
 * 5. Internal Dispatch:
 *    The handleOmeWebhook function dispatches to existing
 *    handleStreamStart/handleStreamEnd functions internally
 */

/**
 * Response messages
 */
export const MESSAGES = Object.freeze({
  SUCCESS: "Operation successful.",
  INVALID_INPUT: "Invalid input provided.",
  NOT_FOUND: "The requested resource was not found.",
  SEARCH_KEYWORD: "Keyword is required and must be a string.",
  INTERNAL_SERVER_ERROR:
    "An unexpected error occurred. Please try again later.",
  UNAUTHORIZED: "You are not authorized to perform this action.",
  FORBIDDEN: "Access to this resource is forbidden.",
} as const);

export type MessageKey = keyof typeof MESSAGES;

/**
 * Auth response messages
 */
export const AUTHENTICATION = Object.freeze({
  CREATE_ACCOUNT:
    "Account created successfully. OTP sent on your registered email.",
  LOGIN_ACCOUNT: "Account logged in successfully.",
  SIGNUP_FAILED: "Account signing up failed",
  EMAIL_REQUIRED: "Email is required!",
  USER_EXISTS: "Account with this email already exists!!",
  WRONG_PASS: "Password is incorrect!",
  PROVIDER_TYPE: `Provider type can only be "facebook" | "google" | "email" `,
  LOGOUT_SUCCESS: "Successfully logout",
  UNAUTHORIZED: "You are not authorized to perform this action.",
  PROVIDER_RESTRICTION:
    "You have an existing registration method that prevents this sign-in",
} as const);

export type AuthKey = keyof typeof AUTHENTICATION;

export const APPLE_AUTHENTICATION = Object.freeze({
  CREATE_ACCOUNT: "Account created successfully.",
  LOGIN_ACCOUNT: "Account logged in successfully.",
  SIGNIN_FAILED: "Failed to authenticate with Apple",
  IDENTITY_TOKEN: "Identity token required",
  EMAIL_REQUIRED: "Email is required!",
  USER_EXISTS: "Account with this email already exists!!",
  WRONG_PASS: "Password is incorrect!",
  PROVIDER_TYPE: `Provider type can only be "facebook" | "google" | "email" `,
  LOGOUT_SUCCESS: "Successfully logout",
  UNAUTHORIZED: "You are not authorized: userId is required.",
  INTERESTS: "Interests must not be empty",
} as const);

export type AppleAuthentication = keyof typeof APPLE_AUTHENTICATION;

export const LIVE_STREAM_MESSAGES = Object.freeze({
  SUCCESS: "Operation successful.",
  STARTED: "Live stream started",
  ENDED: "Live stream ended",
  INVALID_INPUT: "Invalid input provided.",
  NOT_FOUND: "The requested resource was not found.",
  INTERNAL_SERVER_ERROR:
    "An unexpected error occurred. Please try again later.",
  UNAUTHORIZED: "You are not authorized to perform this action.",
  FORBIDDEN: "Access to this resource is forbidden.",
});
export type LiveStreamMessage = keyof typeof LIVE_STREAM_MESSAGES;

/**
 * Stream Response messages
 */
export const STREAM_MESSAGES = Object.freeze({
  SUCCESS: "Stream added successfully.",
  FETCH_STREAMS: "Stream list fetched successfully",
  INVALID_INPUT: "Invalid streamId provided.",
  NOT_FOUND: "The requested stream was not found.",
  CHANNEL_STOPPED: "Channel stopped successfully",
  USER_NOT_FOUND: "User not found",
  TOTAL_LIKES_FETCHED: "Total likes fetched successfully",
  ERROR_TOTAL_LIKES: "Error fetching total likes",
  DELETED: "Stream deleted successfully",
  INTERNAL_SERVER_ERROR:
    "An unexpected error occurred. Please try again later.",
  UNAUTHORIZED: "You are not authorized to perform this action.",
  FORBIDDEN: "Access to this resource is forbidden.",
} as const);

export type StreamMessage = keyof typeof STREAM_MESSAGES;

/**
 * Not interested Response messages
 */
export const NOT_INTERESTED_MESSAGES = Object.freeze({
  SUCCESS: "Item added successfully.",
  FETCH_ITEM: "Item list fetched successfully",
  INVALID_INPUT: "Invalid ItemId provided.",
  NOT_FOUND: "The requested Item was not found.",
  CANNOT_ADD: "Cannot add your own Item to not interested",
  USER_NOT_FOUND: "User not found",
  DELETED: "Item removed from not interested successfully",
  ALREADY_EXISTS: "Item already present in not interested",
  INTERNAL_SERVER_ERROR:
    "An unexpected error occurred. Please try again later.",
  UNAUTHORIZED: "You are not authorized to perform this action.",
  FORBIDDEN: "Access to this resource is forbidden.",
} as const);

/**
 * Report stream Response messages
 */
export const REPORT_STREAM_MESSAGES = Object.freeze({
  SUCCESS: "Report submitted successfully",
  UPDATED: "Report status updated successfully",
  FETCH_STREAMS: "Reports fetched successfully",
  INVALID_INPUT: "Invalid streamId provided.",
  NOT_FOUND: "Stream not found",
  CANNOT_REPORT: "Cannot report your own stream.",
  USER_NOT_FOUND: "User not found",
  DELETED: "Stream removed from not interested successfully",
  ALREADY_REPORTED: "You have already reported this stream",
  INTERNAL_SERVER_ERROR:
    "An unexpected error occurred. Please try again later.",
  UNAUTHORIZED: "You are not authorized to perform this action.",
  FORBIDDEN: "Access to this resource is forbidden.",
} as const);

export type ReportStream = keyof typeof REPORT_STREAM_MESSAGES;
/**
 * Share Response messages
 */
export const SHARE_MESSAGES = Object.freeze({
  SUCCESS: "Stream shared successfully.",
  FETCH_STREAMS: "Share list fetched successfully",
  INVALID_INPUT: `Invalid input. Inputs required "streamId" "userId" "platform"`,
  NOT_FOUND: "The requested stream was not found.",
  INTERNAL_SERVER_ERROR:
    "An unexpected error occurred. Please try again later.",
  UNAUTHORIZED: "You are not authorized to perform this action.",
  FORBIDDEN: "Access to this resource is forbidden.",
} as const);

export type ShareMessage = keyof typeof SHARE_MESSAGES;

/**
 * Response messages
 */
export const FEATURED_STREAM_MESSAGES = Object.freeze({
  FETCHED: "Stream list fetched",
  SUCCESS: "Featured stream updated successfully.",
  CREATED: " Featured stream created successfully.",
  INVALID_INPUT: "This stream is already featured.",
  REMOVED: "Stream removed from featured list",
  NOT_FOUND: "No featured streams found for this user.",
  INTERNAL_SERVER_ERROR:
    "An unexpected error occurred. Please try again later.",
  UNAUTHORIZED: "You are not authorized to perform this action.",
  FORBIDDEN: "Access to this resource is forbidden.",
} as const);

export type FeaturedStream = keyof typeof FEATURED_STREAM_MESSAGES;

/**
 * Username check Response messages
 */
export const USERNAME = Object.freeze({
  TAKEN: "Username is already taken",
  AVAILABLE: "Username is available",
  INTERNAL_SERVER_ERROR:
    "An unexpected error occurred. Please try again later.",
} as const);

export type UsernameCheck = keyof typeof USERNAME;

/**
 * User Response messages
 */
export const USER_MESSAGES = Object.freeze({
  SUCCESS: "Operation successfull.",
  UPDATED: "User updated successfully",
  FETCH_STREAMS: "Stream list fetched successfully",
  INVALID_INPUT: "Invalid streamId provided.",
  NOT_FOUND: "The requested stream was not found.",
  NOT_VERIFIED: "User is not verified",
  USER_NOT_FOUND: "User not found",
  DELETED: "Stream deleted successfully",
  INTERNAL_SERVER_ERROR:
    "An unexpected error occurred. Please try again later.",
  FETCHED_FOLLOWERS: "Followers fetched successfully",
  FETCHED_FOLLOWING: "Followings fetched successfully",
  FETCHED_VIEWERS: "Viewers fetched successfully",
  DATABASE_ERROR: "Database error during view registration",
  RECOMMENDED_USERS_FETCHED: "Recommended users fetched successfully",
  PROFILE_VIEW_ERROR: "Error marking profile as viewed",
  CANNOT_VIEW_OWN_PROFILE: "You cannot view your own profile",
  RECOMMENDATION_FAILED: "Failed to fetch recommended users",
  UNAUTHORIZED: "You are not authorized to perform this action.",
  FORBIDDEN: "Access to this resource is forbidden.",
} as const);

export const SAVE_ITEMS = Object.freeze({
  SAVED_SUCCESSFULLY: "Item saved successfully.",
  UNSAVED_SUCCESSFULLY: "Item unsaved successfully.",
  ERROR_SAVING: "Error occurred while saving the item.",
  ERROR_UNSAVING: "Error occurred while unsaving the item.",
  FETCHED_SUCCESSFULLY: "Saved items fetched successfully.",
  ERROR_FETCHING: "Error occurred while fetching saved items.",
} as const);

export type UserMessage = keyof typeof USER_MESSAGES;

/**
 * Interest Response messages
 */
export const INTEREST_MESSAGES = Object.freeze({
  SUCCESS: "User interests added/updated successfully",
  FETCHED: "Interest list fetched successfully",
  INVALID_INPUT: "Invalid interest provided.",
  NOT_FOUND: "The requested interest was not found.",
  USER_NOT_FOUND: "User not found",
  ISVERIFIED: "User must be verified before adding interests",
  DELETED: "Interest deleted successfully",
  INTERNAL_SERVER_ERROR:
    "An unexpected error occurred. Please try adding user interests again.",
  UNAUTHORIZED: "You are not authorized to perform this action.",
  FORBIDDEN: "Access to this resource is forbidden.",
} as const);

export type InterestMessage = keyof typeof INTEREST_MESSAGES;

/**
 * OTP Response messages
 */

export const OTP_MESSAGES = Object.freeze({
  SUCCESS: "OTP sent successfully",
  INVALID_INPUT: "Invalid input provided.",
  NOT_FOUND: "The requested resource was not found.",
  INTERNAL_SERVER_ERROR:
    "An unexpected error occurred. Error verifying OTP. Please try again later.",
  UNAUTHORIZED: "You are not authorized to perform this action.",
  USER_VERIFIED: "User already verified",
  USER_NOT_FOUND: "User not found",
  FORBIDDEN: "Access to this resource is forbidden.",
  EXPIRED: "Invalid or OTP has expired",
  INVALID: "Invalid OTP",
  VERIFIED: "OTP verified successfully",
  RESENT: "OTP resent successfully",
  RESEND_ERROR: "Error re-sending OTP",
} as const);

export const STORY_MESSAGES = Object.freeze({
  CREATED: "Story created successfully.",
  ERROR_CREATING: "An error occurred while creating the story.",
  FETCHED_USER_STORIES: "User stories retrieved successfully.",
  ERROR_FETCHING: "An error occurred while fetching stories.",
  FETCHED_FOLLOWINGS_STORIES: "Followings' stories retrieved successfully.",
  NO_STORIES_FOUND: "No stories found for your followings.",
  ALREADY_VIEWED: "Story already viewed.",
  VIEWED_SUCCESSFULLY: "Story marked as viewed successfully.",
  ERROR_VIEWING: "An error occurred while marking story as viewed.",
  NOT_FOUND: "Story not found.",
} as const);

export const SHORTS_MESSAGES = Object.freeze({
  CREATED: "Short created successfully",
  FETCHED: "Shorts fetched successfully",
  ERROR_CREATING: "Failed to create short",
  ERROR_FETCHING: "Failed to fetch shorts",
  FEED_FETCHED: "Shorts feed fetched successfully",
  ERROR_FETCHING_FEED: "Failed to fetch shorts feed",
  FETCHED_BY_ID: "Short fetched successfully",
  ERROR_FETCHING_BY_ID: "Error fetching short by ID",
  NOT_FOUND: "Short not found",
  DELETED: "Short deleted successfully",
  TRENDING_SCORES_UPDATED: "Trending score updated successfully",
  ERROR_DELETING: "Error deleting short",
  VIEW_ADDED: "View recorded successfully",
  ERROR_ADDING_VIEW: "Failed to record view",
  UNAUTHORIZED_DELETE: "You are not authorized to delete this short",
  FETCHED_BY_AUTHOR: "Shorts retrieved by author successfully",
  ERROR_FETCHING_BY_AUTHOR: "Error fetching shorts by author",
  UPDATED: "Short updated successfully",
  ERROR_UPDATING: "Error updating short",
} as const);

export const PLAYLIST_MESSAGES = Object.freeze({
  CREATED: "Playlist created successfully.",
  DELETED: "Playlist deleted successfully.",
  UPDATED: "Playlist updated successfully.",
  NOT_FOUND: "Playlist not found.",
  ITEM_EXISTS: "Item already exists in playlist.",
  ITEM_ADDED: "Item added to playlist.",
  ITEM_REMOVED: "Item removed from playlist.",
  ITEM_NOT_FOUND: "Item not found in playlist.",
  INTERNAL_ERROR: "Something went wrong with the playlist operation.",
  FETCHED: " Playlist fetched successfully.",
  ERROR_FETCHING: "Error fetching playlist.",
  UNAUTHORIZED: "You are not authorized to perform this action.",
} as const);

export const SHORT_COMMENT_MESSAGES = Object.freeze({
  ADDED: "Comment added successfully",
  FETCHED: "Comments fetched successfully",
  REPLIES_FETCHED: "Replies fetched successfully",
  DELETED: "Comment deleted successfully",
  UNAUTHORIZED: "You are not authorized to delete this comment",
  NOT_FOUND: "Comment not found",
  INTERNAL_ERROR: "Something went wrong while processing comments",
} as const);

export const SHORT_LIKES_MESSAGES = Object.freeze({
  LIKED: "Short liked successfully",
  UNLIKED: "Short unliked successfully",
  INVALID_ACTION: "Invalid like action",
  ERROR_LIKE_ACTION: "Failed to perform like/unlike operation",
} as const);

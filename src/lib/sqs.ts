import {
  DeleteMessageCommand,
  ReceiveMessageCommand,
  SQSClient,
} from "@aws-sdk/client-sqs";
import {
  AWS_ACCESS_KEY_ID,
  AWS_ECS_CLUSTER_URL,
  AWS_ECS_TASK_CONTAINER_NAME,
  AWS_ECS_TASK_DEFINITION,
  AWS_S3_SQS_URL,
  AWS_S3_TRANSCODED_BUCKET_NAME,
  AWS_SECRET_ACCESS_KEY,
} from "../config/environment";
import type { S3Event } from "aws-lambda";
import { ECSClient, RunTaskCommand } from "@aws-sdk/client-ecs";
import { ecsClient, sqsClient } from "../config/s3";
import Stream from "../models/stream/stream.schema";

async function init() {
  const command = new ReceiveMessageCommand({
    QueueUrl: AWS_S3_SQS_URL,
    MaxNumberOfMessages: 1,
    WaitTimeSeconds: 20,
  });

  while (true) {
    const { Messages } = await sqsClient.send(command);

    if (!Messages) {
      console.log("No message in queue");
      continue;
    }

    try {
      for (const message of Messages) {
        const { MessageId, Body } = message;

        console.log("Message Received", { MessageId, Body });

        if (!Body) continue;

        const event = JSON.parse(Body) as S3Event;
        if ("Service" in event && "Event" in event) {
          if (event.Event === "s3:TestEvent") {
            await sqsClient.send(
              new DeleteMessageCommand({
                QueueUrl: AWS_S3_SQS_URL,
                ReceiptHandle: message.ReceiptHandle,
              })
            );
            continue;
          }
        }
        for (const record of event.Records) {
          const { s3 } = record;
          const {
            bucket,
            object: { key },
          } = s3;
          const keyParts = key.split("/");
          const userId = keyParts[1];
          const videoId =
            bucket.name === "ai.metastart.vyoo"
              ? keyParts[2].split("_")[1].replace(".mp4", "")
              : keyParts[2].replace(".mp4", "");

          // spin docker container for hls
          const runTaskCommand = new RunTaskCommand({
            taskDefinition: AWS_ECS_TASK_DEFINITION,
            cluster: AWS_ECS_CLUSTER_URL,
            launchType: "FARGATE",
            networkConfiguration: {
              awsvpcConfiguration: {
                subnets: [
                  "subnet-0685e54f8177c544c",
                  "subnet-0ef36fafbb5a0e34d",
                  "subnet-036aeb50a68803220",
                ],
                securityGroups: ["sg-0f6ad52945ba19e3d"],
                assignPublicIp: "ENABLED",
              },
            },
            overrides: {
              containerOverrides: [
                {
                  name: AWS_ECS_TASK_CONTAINER_NAME,
                  environment: [
                    { name: "BUCKET_NAME", value: bucket.name },
                    { name: "KEY", value: key },
                    { name: "USER", value: userId },
                    { name: "FOLDER_NAME", value: videoId },
                    { name: "MONGODB_URI", value: process.env.MONGODB_URI },
                  ],
                },
              ],
            },
          });
          await ecsClient.send(runTaskCommand);

          //delete message from queue
          await sqsClient.send(
            new DeleteMessageCommand({
              QueueUrl: AWS_S3_SQS_URL,
              ReceiptHandle: message.ReceiptHandle,
            })
          );
          const masterKey = `transcoded/${userId}/${videoId}/master.m3u8`;
          const updatedStream = await Stream.findOneAndUpdate(
            {
              creator: userId,
              url: `https://s3.ap-south-1.amazonaws.com/${bucket.name}/${key}`,
            },
            {
              $set: {
                transcodedUrl: `https://s3.ap-south-1.amazonaws.com/${AWS_S3_TRANSCODED_BUCKET_NAME}/${masterKey}`,
                status: "uploaded",
              },
            }
          );
          console.log("stream transcoded");
          console.log("message delete from queue");
        }
      }
    } catch (err) {
      console.log(err);
    }
  }
}

export default init;

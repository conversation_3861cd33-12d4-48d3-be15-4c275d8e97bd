# OvenMediaEngine Webhook Migration Documentation

## Overview

This document describes the migration from nginx-rtmp webhooks to OvenMediaEngine (OME) AdmissionWebhooks for stream lifecycle management.

## Migration Summary

### Old nginx-rtmp Configuration
```nginx
on_publish http://*************/v1/api/stream/live/start;
on_done http://*************/v1/api/stream/live/end;
notify_method post;
```

**Migration Note:** The old configuration used external IP (*************) but the new OME configuration uses localhost for better performance since both services run on the same machine.

### New OME Configuration
```xml
<AdmissionWebhooks>
    <ControlServerUrl>http://localhost:8081/v1/api/stream/live</ControlServerUrl>
    <SecretKey>ome-webhook-secret-2024</SecretKey>
    <Timeout>5000</Timeout>
    <Enables>
        <Providers>rtmp</Providers>
        <Publishers>rtmp</Publishers>
    </Enables>
</AdmissionWebhooks>
```

**Note:** Using `localhost:8081` instead of the external IP address because both OvenMediaEngine and the API server run on the same machine. This provides:
- Better performance (no external network routing)
- Lower latency for webhook calls
- Reliability even with limited external connectivity
- Best practices for internal service communication

## Key Differences

### 1. Webhook Endpoints
- **nginx-rtmp**: Separate endpoints for start (`/live/start`) and end (`/live/end`)
- **OME**: Single endpoint (`/live`) handles both events based on `request.status` field

### 2. Payload Format

#### nginx-rtmp Payload
```json
{
  "name": "userId_liveStreamId"
}
```

#### OME Payload
```json
{
  "client": {
    "address": "*************",
    "port": 29291,
    "real_ip": "**********",
    "user_agent": "Mozilla/5.0..."
  },
  "request": {
    "direction": "incoming",
    "protocol": "rtmp",
    "status": "opening|closing",
    "url": "rtmp://host:port/app/streamKey",
    "time": "2021-05-12T13:45:00.000Z"
  }
}
```

### 3. Event Detection
- **nginx-rtmp**: Explicit `on_publish` and `on_done` events
- **OME**: Based on `request.status`:
  - `"opening"` + `"incoming"` + `"rtmp"` = Stream start
  - `"closing"` + `"incoming"` + `"rtmp"` = Stream end

### 4. Response Format

#### nginx-rtmp Response
```
HTTP 200 OK
"Stream authorized" (for start)
```

#### OME Response
```json
{
  "allowed": true,
  "reason": "Stream authorized"
}
```

## Implementation Details

### New Webhook Handler
- **File**: `src/controllers/streamControllers/liveStream.controller.ts`
- **Method**: `handleOmeWebhook`
- **Route**: `POST /v1/api/stream/live`

### Internal Dispatch Logic
The `handleOmeWebhook` function:
1. Validates OME webhook payload structure
2. Extracts stream key from URL path
3. Dispatches to existing `handleStreamStart` or `handleStreamEnd` functions
4. Returns appropriate OME response format

### Stream Key Extraction
- **nginx-rtmp**: Direct from `name` field
- **OME**: Extracted from URL path: `rtmp://host:port/app/streamKey` → `streamKey`

### Authentication
- **nginx-rtmp**: Used middleware (`authenticateStartStream`, `authenticateEndStream`)
- **OME**: No authentication middleware (OME handles this via SecretKey in webhook signature)

## Security Considerations

1. **HMAC Signature**: OME includes `X-OME-Signature` header for webhook validation
2. **Secret Key**: Configured in Server.xml (`ome-webhook-secret-2024`)
3. **IP Restriction**: Consider adding IP-based restrictions for webhook endpoint

## Testing

### Manual Testing
1. Start RTMP stream: `rtmp://server:1935/app/streamKey`
2. Check webhook logs for "opening" event
3. Stop stream
4. Check webhook logs for "closing" event

### Payload Validation
- Verify stream key extraction from OME URL format
- Confirm database updates for stream status
- Test socket event emissions

## Backward Compatibility

The old nginx-rtmp endpoints (`/live/start` and `/live/end`) remain functional for any legacy systems or testing purposes.

## Configuration Files Modified

1. **live2/ome_conf/Server.xml**: Added AdmissionWebhooks configuration
2. **src/routes/stream.router.ts**: Added new webhook route
3. **src/controllers/streamControllers/liveStream.controller.ts**: Added webhook handler

## Next Steps

1. Test webhook functionality with live streams
2. Monitor webhook logs for proper event handling
3. Consider implementing webhook signature validation
4. Update monitoring and alerting for new webhook endpoint
